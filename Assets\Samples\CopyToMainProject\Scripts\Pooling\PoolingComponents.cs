using Unity.Entities;
using Unity.Collections;
using Unity.Mathematics;

namespace Rukhanka.Test.Pooling
{
    /// <summary>
    /// Tag component to mark the pooling manager entity
    /// </summary>
    public struct PoolManagerTag : IComponentData { }

    /// <summary>
    /// Component to mark entities as pooled
    /// </summary>
    public struct Pooled : IComponentData, IEnableableComponent { }

    /// <summary>
    /// Component that stores the type hash for pooled entities
    /// </summary>
    public struct PoolTypeComponent : IComponentData
    {
        public uint TypeHash;
    }

    /// <summary>
    /// Buffer element for pool entries configuration
    /// </summary>
    public struct PoolEntry : IBufferElementData
    {
        public Entity Prefab;
        public uint TypeHash;
        // Backward-compat total size (treated as Max if Initial/Max not set)
        public int PoolSize;
        // New: number to pre-instantiate on initialization pass
        public int InitialSize;
        // New: hard cap for total instances allowed (pooled + in-use)
        public int MaxSize;
    }

    /// <summary>
    /// Buffer element for spawn requests
    /// </summary>
    public struct PoolSpawnRequest : IBufferElementData
    {
        public uint TypeHash;
        public Entity RequestingEntity;
        public float3 Position;
        public quaternion Rotation;
        public float Scale;
    }

    /// <summary>
    /// Buffer element for return requests
    /// </summary>
    public struct PoolReturnRequest : IBufferElementData
    {
        public Entity EntityToReturn;
    }

    /// <summary>
    /// Tag to mark that pools have been initialized
    /// </summary>
    public struct PoolInitializedTag : IComponentData { }
    
    public struct NavigationRefreshNeeded : IComponentData
    {
    }

    /// <summary>
    /// Marker component added to entities that were just activated from pool
    /// Used by other systems to detect pooled entities and apply appropriate delays
    /// </summary>
    public struct JustActivatedFromPool : IComponentData
    {
    }

    /// <summary>
    /// Marker component for entities that need their hierarchy disabled after ECB playback
    /// Used during pool initialization to properly disable child entities
    /// </summary>
    public struct NeedsHierarchyDisabled : IComponentData
    {
    }

    /// <summary>
    /// Marker component for entities that need their hierarchy enabled after ECB playback
    /// Used during entity activation to properly enable child entities
    /// </summary>
    public struct NeedsHierarchyEnabled : IComponentData
    {
    }

    /// <summary>
    /// Marker component for entities that need to be checked for hierarchy after ECB playback
    /// Used to determine if newly created entities need hierarchy processing
    /// </summary>
    public struct NeedsHierarchyCheck : IComponentData
    {
    }
}

