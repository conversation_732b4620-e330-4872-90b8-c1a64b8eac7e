using PlayerFAP.Components;
using PlayerFAP.Tags;
using ProjectDawn.Navigation.Sample.Crowd;
using Unity.Burst;
using Unity.Collections;
using Unity.Entities;
using Unity.Mathematics;
using Unity.Transforms;
using UnityEngine;
using RagdollSystem;
using Unity.Rendering;

namespace Rukhanka.Test.Pooling
{
    /// <summary>
    /// High-performance pooling system with advanced DOTS optimizations
    /// - Burst-compiled hot paths with managed fallbacks
    /// - Zero-allocation entity lookup via hash maps
    /// - Cached component handles and queries
    /// - Parallel processing support
    /// </summary>
    [UpdateInGroup(typeof(InitializationSystemGroup))]
    [RequireMatchingQueriesForUpdate]
    [BurstCompile]
    public partial struct PoolingSystem : ISystem
    {
        // Cached queries with specific archetypes for maximum performance
        private EntityQuery m_PoolManagerQuery;
        private EntityQuery m_SpawnRequestQuery;
        private EntityQuery m_ReturnRequestQuery;
        private EntityQuery m_PooledEntityQuery;
        private EntityQuery m_PoolCountQuery;

        private EntityQueryMask m_AllArchetypesMask;

        // Cached component handles (updated once per frame)
        private ComponentTypeHandle<PoolTypeComponent> m_PoolTypeHandle;
        private ComponentTypeHandle<Pooled> m_PooledHandle;
        private ComponentTypeHandle<InUse> m_InUseHandle;
        private ComponentTypeHandle<Disabled> m_DisabledHandle;
        private ComponentTypeHandle<LocalTransform> m_TransformHandle;
        private BufferTypeHandle<PoolEntry> m_PoolEntryHandle;
        private BufferTypeHandle<PoolSpawnRequest> m_SpawnRequestHandle;
        private BufferTypeHandle<PoolReturnRequest> m_ReturnRequestHandle;
        private BufferTypeHandle<Child> m_ChildHandle;
        private BufferTypeHandle<LinkedEntityGroup> m_LinkedEntityHandle;

        // Cached lookups for fast component access
        private ComponentLookup<PoolTypeComponent> m_PoolTypeLookup;
        private ComponentLookup<Pooled> m_PooledLookup;
        private ComponentLookup<InUse> m_InUseLookup;
        private ComponentLookup<Disabled> m_DisabledLookup;
        private ComponentLookup<EnemyTag> m_EnemyTagLookup;
        private ComponentLookup<SpawnerLink> m_SpawnerLinkLookup;
        private ComponentLookup<Parent> m_ParentLookup;
        private BufferLookup<PoolEntry> m_PoolEntryLookup;
        private BufferLookup<Child> m_ChildLookup;
        private BufferLookup<LinkedEntityGroup> m_LinkedEntityLookup;

        // Pre-allocated collections (reused every frame to avoid GC)
        private NativeParallelMultiHashMap<uint, Entity> m_TypeHashToEntityMap;
        private NativeList<Entity> m_TempEntityList;
        private NativeList<PoolTypeComponent> m_TempPoolTypeList;
        private NativeHashMap<uint, int> m_TypeHashCounts;

        // OnCreate cannot be Burst compiled due to managed object creation (EntityQuery)
        public void OnCreate(ref SystemState state)
        {
            state.RequireForUpdate<EndInitializationEntityCommandBufferSystem.Singleton>();
            state.RequireForUpdate<PoolManagerTag>();

            // Initialize all cached queries with specific archetypes
            InitializeQueries(ref state);

            // Initialize all component handles and lookups
            InitializeHandlesAndLookups(ref state);

            // Initialize pre-allocated collections
            InitializeCollections();


            // Validate pool prefabs in editor
#if UNITY_EDITOR
            ValidatePoolPrefabs(ref state);
#endif
        }

        private void InitializeQueries(ref SystemState state)
        {
            // Pool manager query with exclusion for one-time initialization
            m_PoolManagerQuery = state.GetEntityQuery(
                ComponentType.ReadOnly<PoolManagerTag>(),
                ComponentType.ReadOnly<PoolEntry>(),
                ComponentType.Exclude<PoolInitializedTag>()
            );

            // Spawn request query
            m_SpawnRequestQuery = state.GetEntityQuery(
                ComponentType.ReadOnly<PoolManagerTag>(),
                ComponentType.ReadOnly<PoolSpawnRequest>()
            );

            // Return request query
            m_ReturnRequestQuery = state.GetEntityQuery(
                ComponentType.ReadOnly<PoolManagerTag>(),
                ComponentType.ReadOnly<PoolReturnRequest>()
            );

            // Pooled entities query with disabled filter
            m_PooledEntityQuery = state.GetEntityQuery(
                ComponentType.ReadOnly<Pooled>(),
                ComponentType.ReadOnly<PoolTypeComponent>(),
                ComponentType.ReadOnly<Disabled>()
            );

            // Pool count query for initialization
            m_PoolCountQuery = state.GetEntityQuery(
                ComponentType.ReadOnly<Pooled>(),
                ComponentType.ReadOnly<PoolTypeComponent>()
            );

            var allQuery = state.EntityManager.CreateEntityQuery(ComponentType.ReadOnly<LocalTransform>());
            m_AllArchetypesMask = allQuery.GetEntityQueryMask();
            allQuery.Dispose();
        }

        private void InitializeHandlesAndLookups(ref SystemState state)
        {
            // Component type handles (will be updated each frame)
            m_PoolTypeHandle = state.GetComponentTypeHandle<PoolTypeComponent>(true);
            m_PooledHandle = state.GetComponentTypeHandle<Pooled>(false);
            m_InUseHandle = state.GetComponentTypeHandle<InUse>(false);
            m_DisabledHandle = state.GetComponentTypeHandle<Disabled>(false);
            m_TransformHandle = state.GetComponentTypeHandle<LocalTransform>(false);

            // Buffer type handles
            m_PoolEntryHandle = state.GetBufferTypeHandle<PoolEntry>(true);
            m_SpawnRequestHandle = state.GetBufferTypeHandle<PoolSpawnRequest>(false);
            m_ReturnRequestHandle = state.GetBufferTypeHandle<PoolReturnRequest>(false);
            m_ChildHandle = state.GetBufferTypeHandle<Child>(true);
            m_LinkedEntityHandle = state.GetBufferTypeHandle<LinkedEntityGroup>(true);

            // Component lookups for random access
            m_PoolTypeLookup = state.GetComponentLookup<PoolTypeComponent>(true);
            m_PooledLookup = state.GetComponentLookup<Pooled>(false);
            m_InUseLookup = state.GetComponentLookup<InUse>(false);
            m_DisabledLookup = state.GetComponentLookup<Disabled>(false);
            m_EnemyTagLookup = state.GetComponentLookup<EnemyTag>(true);
            m_SpawnerLinkLookup = state.GetComponentLookup<SpawnerLink>(false);

            // Buffer lookups
            m_PoolEntryLookup = state.GetBufferLookup<PoolEntry>(true);
            m_ChildLookup = state.GetBufferLookup<Child>(true);
            m_LinkedEntityLookup = state.GetBufferLookup<LinkedEntityGroup>(true);
            m_ParentLookup = state.GetComponentLookup<Parent>(false);
        }

        private void InitializeCollections()
        {
            // Pre-allocate collections with reasonable initial capacity
            m_TypeHashToEntityMap = new NativeParallelMultiHashMap<uint, Entity>(256, Allocator.Persistent);
            m_TempEntityList = new NativeList<Entity>(64, Allocator.Persistent);
            m_TempPoolTypeList = new NativeList<PoolTypeComponent>(64, Allocator.Persistent);
            m_TypeHashCounts = new NativeHashMap<uint, int>(32, Allocator.Persistent);
        }

#if UNITY_EDITOR
        private void ValidatePoolPrefabs(ref SystemState state)
        {
            // Validate that all pool prefabs have required components
            foreach (var (poolBuffer, entity) in SystemAPI.Query<DynamicBuffer<PoolEntry>>().WithEntityAccess())
            {
                foreach (var poolEntry in poolBuffer)
                {
                    var prefab = poolEntry.Prefab;
                    if (prefab == Entity.Null)
                    {
                        Debug.LogError($"Pool entry has null prefab on entity {entity}");
                        continue;
                    }

                    // Check for required components
                    if (!state.EntityManager.HasComponent<Pooled>(prefab))
                        Debug.LogWarning($"Pool prefab {prefab} missing Pooled component");
                    if (!state.EntityManager.HasComponent<InUse>(prefab))
                        Debug.LogWarning($"Pool prefab {prefab} missing InUse component");
                }
            }
        }
#endif

        public void OnDestroy(ref SystemState state)
        {
            // Dispose all native collections
            if (m_TypeHashToEntityMap.IsCreated) m_TypeHashToEntityMap.Dispose();
            if (m_TempEntityList.IsCreated) m_TempEntityList.Dispose();
            if (m_TempPoolTypeList.IsCreated) m_TempPoolTypeList.Dispose();
            if (m_TypeHashCounts.IsCreated) m_TypeHashCounts.Dispose();
        }

        // Cannot be Burst compiled due to ProcessReturnRequests calling managed EntitiesGraphicsSystem
        public void OnUpdate(ref SystemState state)
        {
            // Update all component handles and lookups once per frame
            UpdateHandlesAndLookups(ref state);

            // Clear and rebuild the type hash to entity map for fast lookups
            RebuildTypeHashMap(ref state);

            var ecbSystem = SystemAPI.GetSingleton<EndInitializationEntityCommandBufferSystem.Singleton>();
            var ecb = ecbSystem.CreateCommandBuffer(state.WorldUnmanaged);

            // 1) Initialize pools once at startup (Burst optimized)
            InitializePools(ref state, ecb);

            // 2) Process spawn requests (Burst optimized)
            ProcessSpawnRequests(ref state, ecb);

            // 3) Process return requests (hybrid: Burst + managed)
            ProcessReturnRequests(ref state, ecb);

            // 4) Process entities that need hierarchy disabled/enabled (after ECB playback)
            ProcessHierarchyDisabling(ref state);
            ProcessHierarchyEnabling(ref state);
            ProcessHierarchyCheck(ref state);

#if UNITY_EDITOR
            // Validate internal consistency in editor
            state.EntityManager.Debug.CheckInternalConsistency();
#endif
        }

        private void UpdateHandlesAndLookups(ref SystemState state)
        {
            // Update component type handles
            m_PoolTypeHandle.Update(ref state);
            m_PooledHandle.Update(ref state);
            m_InUseHandle.Update(ref state);
            m_DisabledHandle.Update(ref state);
            m_TransformHandle.Update(ref state);

            // Update buffer type handles
            m_PoolEntryHandle.Update(ref state);
            m_SpawnRequestHandle.Update(ref state);
            m_ReturnRequestHandle.Update(ref state);
            m_ChildHandle.Update(ref state);
            m_LinkedEntityHandle.Update(ref state);

            // Update component lookups
            m_PoolTypeLookup.Update(ref state);
            m_PooledLookup.Update(ref state);
            m_InUseLookup.Update(ref state);
            m_DisabledLookup.Update(ref state);
            m_EnemyTagLookup.Update(ref state);
            m_SpawnerLinkLookup.Update(ref state);

            // Update buffer lookups
            m_PoolEntryLookup.Update(ref state);
            m_ChildLookup.Update(ref state);
            m_LinkedEntityLookup.Update(ref state);
        }

        [BurstCompile]
        private void RebuildTypeHashMap(ref SystemState state)
        {
            // Clear the map and rebuild it for O(1) entity lookup by type hash
            m_TypeHashToEntityMap.Clear();

            // Use cached query and handles for maximum performance
            var chunks = m_PooledEntityQuery.ToArchetypeChunkArray(Allocator.Temp);

            foreach (var chunk in chunks)
            {
                var entities = chunk.GetNativeArray(state.GetEntityTypeHandle());
                var poolTypes = chunk.GetNativeArray(ref m_PoolTypeHandle);

                for (int i = 0; i < entities.Length; i++)
                {
                    m_TypeHashToEntityMap.Add(poolTypes[i].TypeHash, entities[i]);
                }
            }

            chunks.Dispose();
        }

        [BurstCompile]
        private void InitializePools(ref SystemState state, EntityCommandBuffer ecb)
        {
            // Use cached query - only runs once due to WithNone<PoolInitializedTag>
            if (m_PoolManagerQuery.IsEmpty) return;

            using var entities = m_PoolManagerQuery.ToEntityArray(Allocator.Temp);

            for (int i = 0; i < entities.Length; i++)
            {
                var entity = entities[i];
                var poolBuffer = SystemAPI.GetBuffer<PoolEntry>(entity);

                // Pre-count all existing pooled entities by type hash for efficiency
                var typeHashCounts = new NativeHashMap<uint, int>(poolBuffer.Length, Allocator.Temp);

                using var pooledEntities = m_PoolCountQuery.ToEntityArray(Allocator.Temp);
                using var poolTypes = m_PoolCountQuery.ToComponentDataArray<PoolTypeComponent>(Allocator.Temp);

                for (int j = 0; j < poolTypes.Length; j++)
                {
                    var typeHash = poolTypes[j].TypeHash;
                    if (typeHashCounts.TryGetValue(typeHash, out int count))
                        typeHashCounts[typeHash] = count + 1;
                    else
                        typeHashCounts[typeHash] = 1;
                }

                foreach (var entry in poolBuffer)
                {
                    // Determine desired initial and max sizes with backward-compat fallbacks
                    int desiredInitial = entry.InitialSize > 0 ? entry.InitialSize : entry.PoolSize;
                    int desiredMax = entry.MaxSize > 0 ? entry.MaxSize : entry.PoolSize;
                    desiredInitial = math.min(desiredInitial, desiredMax);

                    // Get existing count from pre-computed hash map
                    int pooledCount = typeHashCounts.TryGetValue(entry.TypeHash, out int count) ? count : 0;
                    int toCreate = math.max(0, desiredInitial - pooledCount);

                    for (int k = 0; k < toCreate; k++)
                    {
                        var pooledEntity = ecb.Instantiate(entry.Prefab);
                        ecb.AddComponent<Pooled>(pooledEntity);
                        ecb.AddComponent<InUse>(pooledEntity);
                        ecb.AddComponent<Disabled>(pooledEntity);
                        ecb.AddComponent(pooledEntity, new PoolTypeComponent { TypeHash = entry.TypeHash });
                        ecb.SetComponentEnabled<Pooled>(pooledEntity, true);
                        ecb.SetComponentEnabled<InUse>(pooledEntity, false);

                        // Note: We don't add NeedsHierarchyDisabled here because newly created entities
                        // will be immediately activated, which would cause a conflict between
                        // NeedsHierarchyDisabled and NeedsHierarchyEnabled in the same frame
                    }
                }

                typeHashCounts.Dispose();
                ecb.AddComponent<PoolInitializedTag>(entity);
            }
        }

        [BurstCompile]
        private void ProcessSpawnRequests(ref SystemState state, EntityCommandBuffer ecb)
        {
            // Use cached query
            if (m_SpawnRequestQuery.IsEmpty) return;

            using var managerEntities = m_SpawnRequestQuery.ToEntityArray(Allocator.Temp);

            for (int i = 0; i < managerEntities.Length; i++)
            {
                var managerEntity = managerEntities[i];
                var spawnRequests = SystemAPI.GetBuffer<PoolSpawnRequest>(managerEntity);
                if (spawnRequests.Length == 0) continue;

                // Track entities that have already been reserved within this frame to avoid duplicates
                using var reservedEntities = new NativeHashSet<Entity>(spawnRequests.Length, Allocator.Temp);

                // Build per-type caps and current counts so we can ignore requests when caps reached
                var poolEntries = SystemAPI.GetBuffer<PoolEntry>(managerEntity);
                using var maxByType = new NativeHashMap<uint, int>(poolEntries.Length, Allocator.Temp);
                using var countByType = new NativeHashMap<uint, int>(poolEntries.Length, Allocator.Temp);

                // Fill max caps (fallbacks to PoolSize if MaxSize is 0)
                foreach (var pe in poolEntries)
                {
                    int hardCap = pe.MaxSize > 0 ? pe.MaxSize : pe.PoolSize;
                    if (!maxByType.ContainsKey(pe.TypeHash))
                        maxByType.TryAdd(pe.TypeHash, hardCap);
                }

                // Pre-count current totals across world (pooled + in-use)
                using (var poolTypes = m_PoolCountQuery.ToComponentDataArray<PoolTypeComponent>(Allocator.Temp))
                {
                    for (int t = 0; t < poolTypes.Length; t++)
                    {
                        var th = poolTypes[t].TypeHash;
                        if (countByType.TryGetValue(th, out int c))
                        {
                            // update: remove and re-add to avoid indexer assignment on using var
                            countByType.Remove(th);
                            countByType.TryAdd(th, c + 1);
                        }
                        else
                        {
                            countByType.TryAdd(th, 1);
                        }
                    }
                }

                for (int j = 0; j < spawnRequests.Length; j++)
                {
                    var request = spawnRequests[j];

                    // If we've reached cap for this type, ignore this request
                    if (maxByType.TryGetValue(request.TypeHash, out int cap))
                    {
                        int cur = countByType.TryGetValue(request.TypeHash, out int c) ? c : 0;
                        if (cur >= cap)
                        {
                            // skip processing this request; we still clear the buffer after loop
                            continue;
                        }
                    }

                    // Try to fetch a unique pooled entity not yet reserved this frame
                    Entity pooledEntity = GetPooledEntity(ref state, request.TypeHash, reservedEntities);
                    bool isExistingEntity = !pooledEntity.Equals(Entity.Null);

                    // If no pooled entity available, create a new one
                    if (pooledEntity.Equals(Entity.Null))
                    {
                        pooledEntity = CreateNewPooledEntity(ref state, ecb, managerEntity, request.TypeHash);
                        isExistingEntity = false; // This is a newly created entity
                        if (!pooledEntity.Equals(Entity.Null))
                        {
                            // Update our per-type count as we've just created a new entity
                            int cur = countByType.TryGetValue(request.TypeHash, out int c) ? c : 0;
                            if (cur > 0)
                            {
                                countByType.Remove(request.TypeHash);
                                countByType.TryAdd(request.TypeHash, cur + 1);
                            }
                            else
                            {
                                countByType.TryAdd(request.TypeHash, 1);
                            }
                        }
                    }

                    if (!pooledEntity.Equals(Entity.Null))
                    {
                        // Mark as reserved so subsequent requests won't reuse it in the same frame
                        reservedEntities.Add(pooledEntity);

                        ActivatePooledEntity(ref state, ecb, pooledEntity, request, isExistingEntity);
                    }
                }

                // Clear buffer via ECB for deterministic playback
                ecb.SetBuffer<PoolSpawnRequest>(managerEntity).Clear();
            }
        }

        // Cannot be Burst compiled due to ReturnToPool calling managed EntitiesGraphicsSystem
        private void ProcessReturnRequests(ref SystemState state, EntityCommandBuffer ecb)
        {
            // Use cached query
            if (m_ReturnRequestQuery.IsEmpty) return;

            using var entities = m_ReturnRequestQuery.ToEntityArray(Allocator.Temp);

            for (int i = 0; i < entities.Length; i++)
            {
                var managerEntity = entities[i];
                var returnRequests = SystemAPI.GetBuffer<PoolReturnRequest>(managerEntity);

                if (returnRequests.Length == 0) continue;

                for (int j = 0; j < returnRequests.Length; j++)
                {
                    var request = returnRequests[j];
                    ReturnToPool(ref state, ecb, request.EntityToReturn);
                }

                // Clear buffer via ECB for deterministic playback
                ecb.SetBuffer<PoolReturnRequest>(managerEntity).Clear();
            }
        }

        [BurstCompile]
        private Entity GetPooledEntity(ref SystemState state, uint typeHash, NativeHashSet<Entity> reserved)
        {
            // Attempt to iterate through all entities with the requested hash until we find one
            // that is (1) marked as Pooled (enabled), (2) NOT InUse, and (3) not already reserved this frame.
            if (m_TypeHashToEntityMap.TryGetFirstValue(typeHash, out Entity candidate, out var it))
            {
                do
                {
                    // Skip if already reserved this frame, but continue iterating
                    if (reserved.IsCreated && reserved.Contains(candidate))
                    {
                        continue;
                    }

                    bool isPooled = m_PooledLookup.HasComponent(candidate) &&
                                    m_PooledLookup.IsComponentEnabled(candidate);
                    bool isInUse = m_InUseLookup.HasComponent(candidate) &&
                                   m_InUseLookup.IsComponentEnabled(candidate);

                    if (isPooled && !isInUse)
                    {
                        return candidate;
                    }
                } while (m_TypeHashToEntityMap.TryGetNextValue(out candidate, ref it));
            }

            return Entity.Null;
        }

        [BurstCompile]
        private Entity GetPooledEntityWithFallback(ref SystemState state, uint typeHash)
        {
            // Try hash map first (O(1) lookup)
            if (m_TypeHashToEntityMap.TryGetFirstValue(typeHash, out Entity entity, out var iterator))
            {
                return entity;
            }

            // Fallback to direct query if hash map is empty (shouldn't happen in normal operation)
            var chunks = m_PooledEntityQuery.ToArchetypeChunkArray(Allocator.Temp);

            foreach (var chunk in chunks)
            {
                var entities = chunk.GetNativeArray(state.GetEntityTypeHandle());
                var poolTypes = chunk.GetNativeArray(ref m_PoolTypeHandle);

                for (int i = 0; i < entities.Length; i++)
                {
                    if (poolTypes[i].TypeHash == typeHash)
                    {
                        chunks.Dispose();
                        return entities[i];
                    }
                }
            }

            chunks.Dispose();
            return Entity.Null;
        }

        [BurstCompile]
        private Entity CreateNewPooledEntity(ref SystemState state, EntityCommandBuffer ecb, Entity managerEntity,
            uint typeHash)
        {
            var poolEntries = SystemAPI.GetBuffer<PoolEntry>(managerEntity);

            // Find configured pool size and prefab for this type
            int configuredPoolSize = -1;
            int configuredMaxSize = -1;
            Entity prefab = Entity.Null;
            foreach (var entry in poolEntries)
            {
                if (entry.TypeHash == typeHash)
                {
                    configuredPoolSize = entry.PoolSize;
                    configuredMaxSize = entry.MaxSize > 0 ? entry.MaxSize : entry.PoolSize;
                    prefab = entry.Prefab;
                    break;
                }
            }

            // If this type is not configured, do not create
            if (prefab == Entity.Null)
                return Entity.Null;

            // Enforce pool cap: count existing (pooled + in-use) entities of this type
            if (configuredPoolSize >= 0)
            {
                using var poolTypes = m_PoolCountQuery.ToComponentDataArray<PoolTypeComponent>(Allocator.Temp);
                int currentCount = 0;
                for (int i = 0; i < poolTypes.Length; i++)
                {
                    if (poolTypes[i].TypeHash == typeHash)
                        currentCount++;
                }

                // Use configuredMaxSize as the hard cap (fallback to configuredPoolSize)
                int hardCap = configuredMaxSize > 0 ? configuredMaxSize : configuredPoolSize;
                if (currentCount >= hardCap)
                {
                    // Pool exhausted: do not instantiate beyond cap
                    return Entity.Null;
                }
            }

            // Create a new pooled entity within cap
            var newEntity = ecb.Instantiate(prefab);
            ecb.AddComponent<Pooled>(newEntity);
            ecb.AddComponent<InUse>(newEntity);
            ecb.AddComponent<Disabled>(newEntity);
            ecb.AddComponent(newEntity, new PoolTypeComponent { TypeHash = typeHash });
            ecb.SetComponentEnabled<Pooled>(newEntity, true);
            ecb.SetComponentEnabled<InUse>(newEntity, false);
            return newEntity;
        }

        // Cannot be Burst compiled due to complex entity existence checks and managed object access
        private void ActivatePooledEntity(ref SystemState state, EntityCommandBuffer ecb, Entity entity,
            PoolSpawnRequest request, bool isExistingEntity)
        {
            // For deferred entities (newly created), we can't use SystemAPI.HasComponent because the
            // entity hasn’t been played back yet. Determine whether the entity already exists:
            // Use isExistingEntity parameter instead of calling EntityManager.Exists to avoid ECB error

            // Disable Pooled component (assume it exists for pooled entities)
            ecb.SetComponentEnabled<Pooled>(entity, false);

            // Enable InUse component (assume it exists for pooled entities)
            ecb.SetComponentEnabled<InUse>(entity, true);

            // Remove Disabled component from root entity
            ecb.RemoveComponent<Disabled>(entity);

            // Enable the entire LinkedEntityGroup hierarchy (only for entities that have hierarchies)
            if (isExistingEntity)
            {
                // Check if entity actually has a hierarchy before processing
                if (SystemAPI.HasBuffer<LinkedEntityGroup>(entity) || SystemAPI.HasBuffer<Child>(entity))
                {
                    ProcessEntityHierarchy(ref state, ecb, entity, true, isExistingEntity);
                }
                // For simple entities (like drop items), no hierarchy processing needed
            }
            else
            {
                // For newly created entities, only add hierarchy processing if they have hierarchies
                // This prevents simple entities like drop items from getting unnecessary processing
                ecb.AddComponent<NeedsHierarchyCheck>(entity);
            }

            // First restore original state (materials, physics, etc.) but NOT position
            if (isExistingEntity)
            {
                RestoreOriginalState(ref state, ecb, entity, skipPositionRestore: true);
            }

            // THEN set the new transform from the spawn request (this must come AFTER RestoreOriginalState)
            ecb.SetComponent(entity, new LocalTransform
            {
                Position = request.Position,
                Rotation = request.Rotation,
                Scale = request.Scale == 0f ? 1f : request.Scale
            });

            // Handle enemy-specific activation
            if (isExistingEntity && SystemAPI.HasComponent<EnemyTag>(entity))
            {
                // Reset navigation components for proper pathfinding
                if (SystemAPI.HasComponent<ProjectDawn.Navigation.AgentBody>(entity))
                {
                    ecb.SetComponentEnabled<ProjectDawn.Navigation.AgentBody>(entity, true);
                }

                // Disable agent collider first - will be re-enabled in next frame by NavigationRefreshSystem
                if (SystemAPI.HasComponent<ProjectDawn.Navigation.AgentCollider>(entity))
                {
                    ecb.SetComponentEnabled<ProjectDawn.Navigation.AgentCollider>(entity, false);
                    ecb.AddComponent<NavigationRefreshNeeded>(entity);
                }
            }

            // Add SpawnerLink component to track which spawner created this entity
            if (request.RequestingEntity != Entity.Null)
            {
                ecb.AddComponent(entity, new SpawnerLink
                {
                    SpawnerEntity = request.RequestingEntity,
                    SpawnerName = "PooledSpawner"
                });
            }

            // Add marker component to indicate this entity was just activated from pool
            if (isExistingEntity)
            {
                ecb.AddComponent<JustActivatedFromPool>(entity);
            }
        }

        // Cannot be Burst compiled due to RestoreStateForEntity calling managed EntitiesGraphicsSystem
        private void RestoreOriginalState(ref SystemState state, EntityCommandBuffer ecb, Entity entity,
            bool skipPositionRestore = false)
        {
            // Restore position state (only when returning to pool, not when activating)
            if (!skipPositionRestore && SystemAPI.HasComponent<InitPositionState>(entity))
            {
                var positionState = SystemAPI.GetComponent<InitPositionState>(entity);
                ecb.SetComponent(entity, new LocalTransform
                {
                    Position = positionState.OriginalPosition,
                    Rotation = positionState.OriginalRotation,
                    Scale = 1f
                });
            }

            // Restore material state for ragdoll entities
            if (SystemAPI.HasComponent<RagdollCharacterTag>(entity))
            {
                RestoreStateForEntity(ref state, ecb, entity);
            }

            // Remove temporary components added during ragdoll lifecycle
            RemoveTemporaryComponents(ref state, ecb, entity);
        }






        // Cannot be Burst compiled due to EntitiesGraphicsSystem managed object access
        private void RestoreStateForEntity(ref SystemState state, EntityCommandBuffer ecb, Entity entity)
        {
            // Check renderer entities in ragdoll collider buffer
            if (SystemAPI.HasBuffer<DissolveOverride>(entity))
            {
                var dissolveBuf = SystemAPI.GetBuffer<DissolveOverride>(entity);

                // 2. Grab the material we want to restore to
                //    (stored as a UnityEngine.Material reference on the root)
                var hasMatRef = SystemAPI.HasComponent<OriginalMaterialRef>(entity);

                // 3. For every renderer that belongs to this rag-doll
                foreach (var d in dissolveBuf)
                {
                    var renderer = d.RendererEntity;

                    // 1. Grab the material ID that Rukhanka already registered
                    if (hasMatRef && SystemAPI.HasComponent<MaterialMeshInfo>(renderer))
                    {
                        var egSystem = state.World.GetExistingSystemManaged<EntitiesGraphicsSystem>();

                        var material = SystemAPI.GetComponent<OriginalMaterialRef>(entity);

                        var batchId = egSystem.RegisterMaterial(material.Asset);

                        var mmi = SystemAPI.GetComponent<MaterialMeshInfo>(renderer);
                        mmi.MaterialID = batchId;
                        ecb.SetComponent(renderer, mmi); // keeps the same ID, just restores if it was overwritten
                    }

                    // 2. Restore cut-off
                    if (SystemAPI.HasComponent<InitCutoutState>(renderer))
                    {
                        var cut = SystemAPI.GetComponent<InitCutoutState>(renderer);
                        ecb.SetComponent(renderer, new URPCutoff { Value = cut.OriginalCutoffValue });
                        ecb.SetComponentEnabled<URPCutoff>(renderer, false);
                    }
                }
            }
        }


        [BurstCompile]
        private void RemoveTemporaryComponents(ref SystemState state, EntityCommandBuffer ecb, Entity entity)
        {
            // Remove ragdoll-related temporary components
            if (SystemAPI.HasComponent<RagdollCharacterTag>(entity))
            {
                if (SystemAPI.HasComponent<RagdollIsFinished>(entity))
                    ecb.RemoveComponent<RagdollIsFinished>(entity);

                if (SystemAPI.HasComponent<DeathTimerComponent>(entity))
                    ecb.RemoveComponent<DeathTimerComponent>(entity);

                if (SystemAPI.HasComponent<DissolveTween>(entity))
                    ecb.RemoveComponent<DissolveTween>(entity);

                if (SystemAPI.HasComponent<RagdollHitImpulse>(entity))
                    ecb.RemoveComponent<RagdollHitImpulse>(entity);

                if (SystemAPI.HasComponent<RagdollImpulseDelay>(entity))
                    ecb.RemoveComponent<RagdollImpulseDelay>(entity);
            }

            // Remove enemy-related temporary components
            if (SystemAPI.HasComponent<EnemyTag>(entity))
            {
                // Remove detection-related temporary tags
                if (SystemAPI.HasComponent<DetectedTag>(entity))
                    ecb.RemoveComponent<DetectedTag>(entity);

                if (SystemAPI.HasComponent<InFOVTag>(entity))
                    ecb.RemoveComponent<InFOVTag>(entity);

                if (SystemAPI.HasComponent<CurrentTargetTag>(entity))
                    ecb.RemoveComponent<CurrentTargetTag>(entity);

                if (SystemAPI.HasComponent<UndetectableTag>(entity))
                    ecb.RemoveComponent<UndetectableTag>(entity);

                // Remove death-related tags
                if (SystemAPI.HasComponent<DeadTag>(entity))
                    ecb.RemoveComponent<DeadTag>(entity);

                // Remove navigation-related temporary components
                if (SystemAPI.HasComponent<NavigationRefreshNeeded>(entity))
                    ecb.RemoveComponent<NavigationRefreshNeeded>(entity);

                // Remove pooling-related temporary components
                if (SystemAPI.HasComponent<JustActivatedFromPool>(entity))
                    ecb.RemoveComponent<JustActivatedFromPool>(entity);

                // Reset health to maximum
                if (SystemAPI.HasComponent<HealthComponent>(entity))
                {
                    var health = SystemAPI.GetComponent<HealthComponent>(entity);
                    health.CurrentHealth = health.MaxHealth;
                    ecb.SetComponent(entity, health);
                }
            }
        }

        // Burst-optimized part of ReturnToPool - handles pooling state and navigation
        [BurstCompile]
        private void ReturnToPoolBurstOptimized(ref SystemState state, EntityCommandBuffer ecb, Entity entity)
        {
            if (SystemAPI.HasComponent<EnemyTag>(entity))
            {
                // Reset navigation components before returning to pool
                if (SystemAPI.HasComponent<ProjectDawn.Navigation.AgentBody>(entity))
                {
                    ecb.SetComponentEnabled<ProjectDawn.Navigation.AgentBody>(entity, false);
                }

                // Disable navigation components
                if (SystemAPI.HasComponent<ProjectDawn.Navigation.AgentCollider>(entity))
                {
                    ecb.SetComponentEnabled<ProjectDawn.Navigation.AgentCollider>(entity, false);
                }

                if (SystemAPI.HasComponent<ProjectDawn.Navigation.AgentSeparation>(entity))
                {
                    ecb.SetComponentEnabled<ProjectDawn.Navigation.AgentSeparation>(entity, false);
                }

                if (SystemAPI.HasComponent<ProjectDawn.Navigation.AgentReciprocalAvoid>(entity))
                {
                    ecb.SetComponentEnabled<ProjectDawn.Navigation.AgentReciprocalAvoid>(entity, false);
                }
            }

            // Only set component enabled states if the components exist
            if (SystemAPI.HasComponent<Pooled>(entity))
            {
                ecb.SetComponentEnabled<Pooled>(entity, true);
            }
            else
            {
                ecb.AddComponent<Pooled>(entity);
                ecb.SetComponentEnabled<Pooled>(entity, true);
            }

            if (SystemAPI.HasComponent<InUse>(entity))
            {
                ecb.SetComponentEnabled<InUse>(entity, false);
            }
            else
            {
                ecb.AddComponent<InUse>(entity);
                ecb.SetComponentEnabled<InUse>(entity, false);
            }

            // Add Disabled component to root entity
            if (!SystemAPI.HasComponent<Disabled>(entity))
            {
                ecb.AddComponent<Disabled>(entity);
            }

            // Disable the entire LinkedEntityGroup hierarchy (only for entities that have hierarchies)
            if (SystemAPI.HasBuffer<LinkedEntityGroup>(entity) || SystemAPI.HasBuffer<Child>(entity))
            {
                // Use ProcessEntityHierarchy to avoid ECB/EntityManager conflicts
                // Entity exists when returning to pool, so pass true for isExistingEntity
                ProcessEntityHierarchy(ref state, ecb, entity, false, true);
            }
            // For simple entities (like drop items), no hierarchy processing needed

            // Remove SpawnerLink when returning to pool
            if (SystemAPI.HasComponent<SpawnerLink>(entity))
            {
                ecb.RemoveComponent<SpawnerLink>(entity);
            }
        }

        // Cannot be Burst compiled due to RestoreOriginalState calling managed EntitiesGraphicsSystem
        private void ReturnToPool(ref SystemState state, EntityCommandBuffer ecb, Entity entity)
        {
            // Execute Burst-optimized part first
            ReturnToPoolBurstOptimized(ref state, ecb, entity);

            // Then execute managed part (material restoration)
            RestoreOriginalState(ref state, ecb, entity);
        }

        // Helper methods for efficient component operations
        [BurstCompile]
        private void SetComponentEnabledIfExists<T>(EntityCommandBuffer ecb, Entity entity, bool enabled)
            where T : unmanaged, IComponentData, IEnableableComponent
        {
            if (m_DisabledLookup.HasComponent(entity))
            {
                ecb.SetComponentEnabled<T>(entity, enabled);
            }
        }

        [BurstCompile]
        private void AddComponentIfNotExists<T>(EntityCommandBuffer ecb, Entity entity, T component)
            where T : unmanaged, IComponentData
        {
            if (!m_DisabledLookup.HasComponent(entity))
            {
                ecb.AddComponent(entity, component);
            }
        }

        /// <summary>
        /// Process entities that need their hierarchy disabled after ECB playback
        /// This handles newly instantiated pooled entities that need their children disabled
        /// </summary>
        private void ProcessHierarchyDisabling(ref SystemState state)
        {
            // Find all entities that need hierarchy disabling
            var query = state.GetEntityQuery(ComponentType.ReadOnly<NeedsHierarchyDisabled>());
            if (query.IsEmpty) return;

            var entities = query.ToEntityArray(Allocator.Temp);

            #if UNITY_EDITOR
            UnityEngine.Debug.Log($"ProcessHierarchyDisabling: Processing {entities.Length} entities");
            #endif

            foreach (var entity in entities)
            {
                // Ensure entity exists and is fully realized before using EntityManager operations
                if (!state.EntityManager.Exists(entity))
                    continue;

                // Use EntityManager.SetEnabled to disable the entire LinkedEntityGroup
                // This is the proper Unity DOTS way to handle LinkedEntityGroup enabling/disabling
                if (state.EntityManager.HasBuffer<LinkedEntityGroup>(entity))
                {
                    state.EntityManager.SetEnabled(entity, false);
                }
                else
                {
                    // Fallback: manually disable children if no LinkedEntityGroup
                    if (state.EntityManager.HasBuffer<Child>(entity))
                    {
                        var children = state.EntityManager.GetBuffer<Child>(entity);
                        foreach (var child in children)
                        {
                            if (state.EntityManager.Exists(child.Value))
                            {
                                if (!state.EntityManager.HasComponent<Disabled>(child.Value))
                                    state.EntityManager.AddComponent<Disabled>(child.Value);
                            }
                        }
                    }
                }

                // Remove the marker component
                state.EntityManager.RemoveComponent<NeedsHierarchyDisabled>(entity);
            }

            entities.Dispose();
        }

        /// <summary>
        /// Process entities that need their hierarchy enabled after ECB playback
        /// This handles newly activated entities that need their children enabled
        /// </summary>
        private void ProcessHierarchyEnabling(ref SystemState state)
        {
            // Find all entities that need hierarchy enabling
            var query = state.GetEntityQuery(ComponentType.ReadOnly<NeedsHierarchyEnabled>());
            if (query.IsEmpty) return;

            var entities = query.ToEntityArray(Allocator.Temp);

            #if UNITY_EDITOR
            UnityEngine.Debug.Log($"ProcessHierarchyEnabling: Processing {entities.Length} entities");
            #endif

            foreach (var entity in entities)
            {
                // Ensure entity exists and is fully realized before using EntityManager operations
                if (!state.EntityManager.Exists(entity))
                    continue;

                // Use EntityManager.SetEnabled to enable the entire LinkedEntityGroup
                if (state.EntityManager.HasBuffer<LinkedEntityGroup>(entity))
                {
                    state.EntityManager.SetEnabled(entity, true);
                }
                else
                {
                    // Fallback: manually enable children if no LinkedEntityGroup
                    if (state.EntityManager.HasBuffer<Child>(entity))
                    {
                        var children = state.EntityManager.GetBuffer<Child>(entity);
                        foreach (var child in children)
                        {
                            if (state.EntityManager.Exists(child.Value))
                            {
                                if (state.EntityManager.HasComponent<Disabled>(child.Value))
                                    state.EntityManager.RemoveComponent<Disabled>(child.Value);
                            }
                        }
                    }
                }

                // Remove the marker component
                state.EntityManager.RemoveComponent<NeedsHierarchyEnabled>(entity);
            }

            entities.Dispose();
        }

        /// <summary>
        /// Process entities that need hierarchy check after ECB playback
        /// This determines if newly created entities need hierarchy processing
        /// </summary>
        private void ProcessHierarchyCheck(ref SystemState state)
        {
            // Find all entities that need hierarchy check
            var query = state.GetEntityQuery(ComponentType.ReadOnly<NeedsHierarchyCheck>());
            if (query.IsEmpty) return;

            var entities = query.ToEntityArray(Allocator.Temp);

            #if UNITY_EDITOR
            UnityEngine.Debug.Log($"ProcessHierarchyCheck: Processing {entities.Length} entities");
            #endif

            foreach (var entity in entities)
            {
                // Ensure entity exists and is fully realized before using EntityManager operations
                if (!state.EntityManager.Exists(entity))
                    continue;

                // Check if entity has hierarchy components
                bool hasHierarchy = state.EntityManager.HasBuffer<LinkedEntityGroup>(entity) ||
                                   state.EntityManager.HasBuffer<Child>(entity);

                if (hasHierarchy)
                {
                    // Entity has hierarchy, enable it
                    if (state.EntityManager.HasBuffer<LinkedEntityGroup>(entity))
                    {
                        state.EntityManager.SetEnabled(entity, true);
                    }
                    else
                    {
                        // Fallback: manually enable children if no LinkedEntityGroup
                        if (state.EntityManager.HasBuffer<Child>(entity))
                        {
                            var children = state.EntityManager.GetBuffer<Child>(entity);
                            foreach (var child in children)
                            {
                                if (state.EntityManager.Exists(child.Value))
                                {
                                    if (state.EntityManager.HasComponent<Disabled>(child.Value))
                                        state.EntityManager.RemoveComponent<Disabled>(child.Value);
                                }
                            }
                        }
                    }
                }
                // For simple entities (no hierarchy), no action needed - they're already enabled

                // Remove the marker component
                state.EntityManager.RemoveComponent<NeedsHierarchyCheck>(entity);
            }

            entities.Dispose();
        }

        /// <summary>
        /// Process entity hierarchy by enabling/disabling entities in LinkedEntityGroup and Child buffers
        /// Uses ECB operations to avoid conflicts with deferred entities
        /// </summary>
        private void ProcessEntityHierarchy(ref SystemState state, EntityCommandBuffer ecb, Entity rootEntity,
            bool enable, bool isExistingEntity = true)
        {
            // Use the parameter to avoid calling EntityManager.Exists on deferred entities

            // Process LinkedEntityGroup first (this is the preferred approach for prefab hierarchies)
            if (isExistingEntity && m_LinkedEntityLookup.HasBuffer(rootEntity))
            {
                var linkedEntities = m_LinkedEntityLookup[rootEntity];
                foreach (var linked in linkedEntities)
                {
                    // Skip root entity (it's handled separately)
                    if (linked.Value == rootEntity)
                        continue;

                    // For existing entities, we can safely assume linked entities exist
                    // For new entities, we skip this check to avoid ECB errors

                    if (enable)
                    {
                        // For enabling: only remove Disabled if it exists (for existing entities)
                        // For new entities, assume they don't have Disabled component yet
                        if (isExistingEntity)
                        {
                            if (state.EntityManager.HasComponent<Disabled>(linked.Value))
                                ecb.RemoveComponent<Disabled>(linked.Value);
                        }
                        // For new entities, we don't need to remove Disabled as they shouldn't have it
                    }
                    else
                    {
                        // For disabling: always add Disabled component
                        if (isExistingEntity)
                        {
                            if (!state.EntityManager.HasComponent<Disabled>(linked.Value))
                                ecb.AddComponent<Disabled>(linked.Value);
                        }
                        else
                        {
                            // For new entities, always add Disabled
                            ecb.AddComponent<Disabled>(linked.Value);
                        }
                    }
                }
            }

            // Also process Child buffer as fallback for entities not in LinkedEntityGroup
            if (isExistingEntity && m_ChildLookup.HasBuffer(rootEntity))
            {
                var children = m_ChildLookup[rootEntity];
                foreach (var child in children)
                {
                    // For existing entities, we can safely assume child entities exist
                    // For new entities, we skip this check to avoid ECB errors

                    // Check if this child is already handled by LinkedEntityGroup
                    bool handledByLinkedGroup = false;
                    if (m_LinkedEntityLookup.HasBuffer(rootEntity))
                    {
                        var linkedEntities = m_LinkedEntityLookup[rootEntity];
                        for (int i = 0; i < linkedEntities.Length; i++)
                        {
                            if (linkedEntities[i].Value == child.Value)
                            {
                                handledByLinkedGroup = true;
                                break;
                            }
                        }
                    }

                    // Only process if not already handled by LinkedEntityGroup
                    if (!handledByLinkedGroup)
                    {
                        if (enable)
                        {
                            // For enabling: only remove Disabled if it exists (for existing entities)
                            if (isExistingEntity)
                            {
                                if (state.EntityManager.HasComponent<Disabled>(child.Value))
                                    ecb.RemoveComponent<Disabled>(child.Value);
                            }
                            // For new entities, we don't need to remove Disabled as they shouldn't have it
                        }
                        else
                        {
                            // For disabling: always add Disabled component
                            if (isExistingEntity)
                            {
                                if (!state.EntityManager.HasComponent<Disabled>(child.Value))
                                    ecb.AddComponent<Disabled>(child.Value);
                            }
                            else
                            {
                                // For new entities, always add Disabled
                                ecb.AddComponent<Disabled>(child.Value);
                            }
                        }
                    }
                }
            }
        }
    }
}