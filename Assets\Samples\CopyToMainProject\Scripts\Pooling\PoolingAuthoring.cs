using System;
using System.Collections.Generic;
using Unity.Entities;
using UnityEngine;
using Data;

namespace Rukhanka.Test.Pooling
{
    public struct UsePoolingSystemTag:IComponentData
    {
        
    }
    /// <summary>
    /// Authoring component for configuring the generic pooling system
    /// </summary>
    public class PoolingAuthoring : MonoBehaviour
    {
        private static PoolingAuthoring _instance;

        public static PoolingAuthoring Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindObjectOfType<PoolingAuthoring>();

                    if (_instance == null)
                    {
                        GameObject managerObject = new GameObject("TechSetting");
                        _instance = managerObject.AddComponent<PoolingAuthoring>();
                    }
                }

                return _instance;
            }
        }
        
        public bool UsePooling;
        
        [Header("Pool Database")]
        [Tooltip("ScriptableObject database containing all pool configurations")]
        public PoolDatabase poolDatabase;
        
        [Header("Manual Configurations (Optional)")]
        [Tooltip("Additional pool configurations not in the database")]
        public List<PoolConfiguration> manualConfigurations = new();
        
        [Serializable]
        public struct PoolConfiguration
        {
            [Header("Enum Configuration")]
            public string enumTypeName;    // e.g., "MyProject.EnemyType"
            public string enumValueName;   // e.g., "Goblin"
            
            [Header("Pool Settings")]
            public GameObject prefab;
            [Min(1)] public int poolSize;
            
            [Header("Runtime (Read-Only)")]
            [SerializeField]
            public uint calculatedHash;
            
            public uint GetCalculatedHash()
            {
                if (!string.IsNullOrEmpty(enumTypeName) && !string.IsNullOrEmpty(enumValueName))
                {
                    var typeHash = enumTypeName.GetHashCode();
                    var valueHash = enumValueName.GetHashCode();
                    return (uint)(typeHash ^ valueHash);
                }
                return 0;
            }
        }

        private void OnValidate()
        {
            // Update calculated hashes in editor for manual configurations
            for (int i = 0; i < manualConfigurations.Count; i++)
            {
                var config = manualConfigurations[i];
                config.calculatedHash = config.GetCalculatedHash();
                manualConfigurations[i] = config;
            }
        }

        class Baker : Baker<PoolingAuthoring>
        {
            public override void Bake(PoolingAuthoring authoring)
            {
                if(!authoring.UsePooling)
                    return;
                
                var entity = GetEntity(TransformUsageFlags.None);

                AddComponent<PoolManagerTag>(entity);
                AddComponent<UsePoolingSystemTag>(entity);
                var poolBuffer = AddBuffer<PoolEntry>(entity);
                AddBuffer<PoolSpawnRequest>(entity);
                AddBuffer<PoolReturnRequest>(entity);

                // Add entries from database
                if (authoring.poolDatabase != null)
                {
                    foreach (var entry in authoring.poolDatabase.entries)
                    {
                        if (!entry.IsValid)
                        {
                            Debug.LogWarning($"PoolingAuthoring: Invalid database entry for {entry.enumTypeName}.{entry.enumValueName}");
                            continue;
                        }

                        poolBuffer.Add(new PoolEntry
                        {
                            Prefab = GetEntity(entry.prefab, TransformUsageFlags.Dynamic),
                            TypeHash = entry.GetCalculatedHash(),
                            PoolSize = authoring.poolDatabase.GetEffectivePoolSize(entry),
                            InitialSize = authoring.poolDatabase.GetEffectiveInitialSize(entry),
                            MaxSize = authoring.poolDatabase.GetEffectiveMaxSize(entry)
                        });
                    }
                }

                // Add manual configurations
                foreach (var config in authoring.manualConfigurations)
                {
                    if (config.prefab == null)
                    {
                        Debug.LogWarning($"PoolingAuthoring: Missing prefab for {config.enumTypeName}.{config.enumValueName}");
                        continue;
                    }

                    poolBuffer.Add(new PoolEntry
                    {
                        Prefab = GetEntity(config.prefab, TransformUsageFlags.Dynamic),
                        TypeHash = config.GetCalculatedHash(),
                        PoolSize = Mathf.Max(1, config.poolSize),
                        InitialSize = Mathf.Max(0, config.poolSize),
                        MaxSize = Mathf.Max(1, config.poolSize)
                    });
                }

                // Validate for duplicate hashes
                var hashSet = new HashSet<uint>();
                for (int i = poolBuffer.Length - 1; i >= 0; i--)
                {
                    var entry = poolBuffer[i];
                    if (hashSet.Contains(entry.TypeHash))
                    {
                        Debug.LogWarning($"PoolingAuthoring: Duplicate hash {entry.TypeHash} found, removing duplicate entry");
                        poolBuffer.RemoveAt(i);
                    }
                    else
                    {
                        hashSet.Add(entry.TypeHash);
                    }
                }
            }
        }
    }
}

